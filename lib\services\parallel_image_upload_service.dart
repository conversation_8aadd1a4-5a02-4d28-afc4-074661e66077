import 'dart:async';
import 'dart:collection';
import 'package:mr_garments_mobile/services/optimized_image_service.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';

/// High-performance parallel image upload service
/// Optimized to send 100 images in ~1 minute like the forward functionality
class ParallelImageUploadService {
  static const int _maxConcurrentUploads = 100; // Increased for maximum speed
  static const Duration _uploadTimeout = Duration(
    seconds: 45,
  ); // Increased timeout for reliability
  static const Duration _retryDelay = Duration(
    milliseconds: 500,
  ); // Delay between retries
  static const int _maxRetries = 2; // Maximum retry attempts per image

  /// Send multiple images with maximum parallel efficiency
  static Future<ParallelUploadResult> uploadImagesInParallel({
    required String chatId,
    required List<ProcessedImage> images,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    Function(int completed, int total, int failed)? onProgress,
    Function(String imageName, UploadStatus status)? onImageStatusUpdate,
  }) async {
    if (images.isEmpty) {
      return ParallelUploadResult(
        success: false,
        totalImages: 0,
        successfulUploads: 0,
        failedUploads: 0,
        errorMessage: 'No images to upload',
      );
    }

    final semaphore = Semaphore(_maxConcurrentUploads);
    final List<String> successfulMessageIds = [];
    final List<String> failedImageNames = [];
    final Map<String, String> uploadErrors = {};

    int completedCount = 0;
    int successCount = 0;
    int failedCount = 0;

    final completer = Completer<ParallelUploadResult>();
    final baseTime = DateTime.now();

    // Create upload tasks for all images
    final uploadTasks =
        images.asMap().entries.map((entry) {
          final index = entry.key;
          final image = entry.value;

          return _createUploadTask(
            semaphore: semaphore,
            chatId: chatId,
            image: image,
            index: index,
            baseTime: baseTime,
            replyToMessageId: index == 0 ? replyToMessageId : null,
            replyToText: index == 0 ? replyToText : null,
            replyToSenderName: index == 0 ? replyToSenderName : null,
            totalImages: images.length,
            onImageStatusUpdate: onImageStatusUpdate,
          );
        }).toList();

    // Process all uploads concurrently
    for (final task in uploadTasks) {
      task
          .then((result) {
            completedCount++;

            if (result.success) {
              successCount++;
              successfulMessageIds.add(result.messageId!);
            } else {
              failedCount++;
              failedImageNames.add(result.imageName);
              if (result.errorMessage != null) {
                uploadErrors[result.imageName] = result.errorMessage!;
              }
            }

            // Update progress
            onProgress?.call(completedCount, images.length, failedCount);

            // Complete when all uploads are done
            if (completedCount == images.length) {
              final overallSuccess = successCount > 0;
              final errorMessage =
                  failedCount > 0
                      ? 'Failed to upload $failedCount out of ${images.length} images'
                      : null;

              completer.complete(
                ParallelUploadResult(
                  success: overallSuccess,
                  totalImages: images.length,
                  successfulUploads: successCount,
                  failedUploads: failedCount,
                  successfulMessageIds: successfulMessageIds,
                  failedImageNames: failedImageNames,
                  uploadErrors: uploadErrors,
                  errorMessage: errorMessage,
                ),
              );
            }
          })
          .catchError((error) {
            completedCount++;
            failedCount++;

            if (completedCount == images.length) {
              completer.complete(
                ParallelUploadResult(
                  success: successCount > 0,
                  totalImages: images.length,
                  successfulUploads: successCount,
                  failedUploads: failedCount,
                  errorMessage: 'Upload process failed: $error',
                ),
              );
            }
          });
    }

    return completer.future;
  }

  /// Create an individual upload task with semaphore control
  static Future<SingleUploadResult> _createUploadTask({
    required Semaphore semaphore,
    required String chatId,
    required ProcessedImage image,
    required int index,
    required DateTime baseTime,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    required int totalImages,
    Function(String imageName, UploadStatus status)? onImageStatusUpdate,
  }) async {
    return semaphore
        .acquire()
        .then((_) async {
          int retryCount = 0;

          while (retryCount <= _maxRetries) {
            try {
              // Notify upload started (only on first attempt)
              if (retryCount == 0) {
                onImageStatusUpdate?.call(image.name, UploadStatus.uploading);
              }

              // Perform the actual upload with timeout
              final uploadFuture = ChatService.sendImageMessage(
                chatId: chatId,
                imageFile: image.compressedFile,
                replyToMessageId: replyToMessageId,
                replyToText: replyToText,
                replyToSenderName: replyToSenderName,
                timestampOverride: baseTime.add(Duration(milliseconds: index)),
                extraMetadata: {
                  'batchIndex': index,
                  'batchTotal': totalImages,
                  'parallelUpload': true,
                  'retryAttempt': retryCount,
                },
              );

              await uploadFuture.timeout(_uploadTimeout);

              // Notify success
              onImageStatusUpdate?.call(image.name, UploadStatus.completed);

              return SingleUploadResult(
                success: true,
                imageName: image.name,
                messageId: '${DateTime.now().millisecondsSinceEpoch}_$index',
              );
            } catch (e) {
              retryCount++;

              // If we've exhausted retries, fail
              if (retryCount > _maxRetries) {
                onImageStatusUpdate?.call(image.name, UploadStatus.failed);

                return SingleUploadResult(
                  success: false,
                  imageName: image.name,
                  errorMessage: 'Failed after $_maxRetries retries: $e',
                );
              }

              // Wait before retry
              await Future.delayed(_retryDelay);
            }
          }

          // This should never be reached, but just in case
          return SingleUploadResult(
            success: false,
            imageName: image.name,
            errorMessage: 'Unexpected error in upload task',
          );
        })
        .whenComplete(() {
          semaphore.release();
        });
  }
}

/// Semaphore implementation for controlling concurrent operations
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}

/// Result of parallel upload operation
class ParallelUploadResult {
  final bool success;
  final int totalImages;
  final int successfulUploads;
  final int failedUploads;
  final List<String> successfulMessageIds;
  final List<String> failedImageNames;
  final Map<String, String> uploadErrors;
  final String? errorMessage;

  ParallelUploadResult({
    required this.success,
    required this.totalImages,
    required this.successfulUploads,
    required this.failedUploads,
    this.successfulMessageIds = const [],
    this.failedImageNames = const [],
    this.uploadErrors = const {},
    this.errorMessage,
  });

  double get successRate =>
      totalImages > 0 ? successfulUploads / totalImages : 0.0;
  bool get hasPartialSuccess => successfulUploads > 0 && failedUploads > 0;
  bool get isCompleteSuccess => successfulUploads == totalImages;
  bool get isCompleteFailure => successfulUploads == 0;
}

/// Result of single image upload
class SingleUploadResult {
  final bool success;
  final String imageName;
  final String? messageId;
  final String? errorMessage;

  SingleUploadResult({
    required this.success,
    required this.imageName,
    this.messageId,
    this.errorMessage,
  });
}

/// Upload status for individual images
enum UploadStatus { pending, uploading, completed, failed }
