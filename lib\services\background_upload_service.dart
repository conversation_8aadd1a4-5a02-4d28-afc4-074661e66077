import 'dart:io';
import 'dart:async';
import 'dart:collection';
import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/local_storage_service.dart';

/// Background upload service for WhatsApp-like image uploading
/// Handles upload queue, retry logic, and status updates
class BackgroundUploadService {
  static BackgroundUploadService? _instance;
  static BackgroundUploadService get instance =>
      _instance ??= BackgroundUploadService._();
  BackgroundUploadService._();

  final Queue<UploadTask> _uploadQueue = Queue<UploadTask>();
  final Map<String, UploadTask> _activeUploads = {};
  final Map<String, StreamController<UploadProgress>> _progressControllers = {};

  bool _isProcessing = false;
  static const int maxConcurrentUploads = 12;
  static const int maxRetries = 3;

  /// Add upload task to queue
  Future<void> queueUpload({
    required String messageId,
    required String chatId,
    required String localImagePath,
    String? thumbnailPath,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    Map<String, dynamic>? metadata,
    Function(UploadProgress)? onProgress,
    Function(String uploadUrl)? onSuccess,
    Function(String error)? onError,
  }) async {
    final task = UploadTask(
      messageId: messageId,
      chatId: chatId,
      localImagePath: localImagePath,
      thumbnailPath: thumbnailPath,
      replyToMessageId: replyToMessageId,
      replyToText: replyToText,
      replyToSenderName: replyToSenderName,
      metadata: metadata,
      onProgress: onProgress,
      onSuccess: onSuccess,
      onError: onError,
    );

    _uploadQueue.add(task);
    _processQueue();
  }

  /// Process upload queue
  Future<void> _processQueue() async {
    if (_isProcessing || _uploadQueue.isEmpty) return;
    if (_activeUploads.length >= maxConcurrentUploads) return;

    _isProcessing = true;

    while (_uploadQueue.isNotEmpty &&
        _activeUploads.length < maxConcurrentUploads) {
      final task = _uploadQueue.removeFirst();
      _activeUploads[task.messageId] = task;
      _processUpload(task);
    }

    _isProcessing = false;
  }

  /// Process individual upload task
  Future<void> _processUpload(UploadTask task) async {
    try {
      // Create progress controller
      final progressController = StreamController<UploadProgress>.broadcast();
      _progressControllers[task.messageId] = progressController;

      // Update progress: Starting
      final startProgress = UploadProgress(
        messageId: task.messageId,
        status: UploadStatus.uploading,
        progress: 0.0,
      );
      progressController.add(startProgress);
      task.onProgress?.call(startProgress);

      // Check if local file exists
      if (!await LocalStorageService.localFileExists(task.localImagePath)) {
        throw Exception('Local file not found: ${task.localImagePath}');
      }

      // Upload to Firebase/backend
      final localFile = File(task.localImagePath);

      // Update progress: 50% (uploading)
      final uploadingProgress = UploadProgress(
        messageId: task.messageId,
        status: UploadStatus.uploading,
        progress: 0.5,
      );
      progressController.add(uploadingProgress);
      task.onProgress?.call(uploadingProgress);

      // Perform actual upload using existing ChatService
      final uploadUrl = await ChatService.uploadFile(localFile, 'images');

      // Update progress: 100% (completed)
      final completedProgress = UploadProgress(
        messageId: task.messageId,
        status: UploadStatus.completed,
        progress: 1.0,
        uploadUrl: uploadUrl,
      );
      progressController.add(completedProgress);
      task.onProgress?.call(completedProgress);
      task.onSuccess?.call(uploadUrl);

      // Don't send message to Firebase here - the optimistic message will be updated
      // The real message creation will be handled by the chat provider

      debugPrint('Upload completed for message: ${task.messageId}');
    } catch (e) {
      debugPrint('Upload failed for message ${task.messageId}: $e');

      // Retry logic
      if (task.retryCount < maxRetries) {
        task.retryCount++;
        debugPrint(
          'Retrying upload for message ${task.messageId} (attempt ${task.retryCount})',
        );

        // Add delay before retry
        await Future.delayed(Duration(seconds: task.retryCount * 2));

        // Re-queue the task
        _uploadQueue.addFirst(task);
      } else {
        // Max retries reached, mark as failed
        final failedProgress = UploadProgress(
          messageId: task.messageId,
          status: UploadStatus.failed,
          progress: 0.0,
          error: e.toString(),
        );

        final progressController = _progressControllers[task.messageId];
        progressController?.add(failedProgress);
        task.onProgress?.call(failedProgress);
        task.onError?.call(e.toString());
      }
    } finally {
      // Clean up
      _activeUploads.remove(task.messageId);
      _progressControllers[task.messageId]?.close();
      _progressControllers.remove(task.messageId);

      // Process next items in queue
      _processQueue();
    }
  }

  // Note: Message creation is handled in the provider after onSuccess via uploadUrl.

  /// Get upload progress stream for a message
  Stream<UploadProgress>? getUploadProgress(String messageId) {
    return _progressControllers[messageId]?.stream;
  }

  /// Check if message is currently uploading
  bool isUploading(String messageId) {
    return _activeUploads.containsKey(messageId);
  }

  /// Get current upload status for a message
  UploadStatus? getUploadStatus(String messageId) {
    if (_activeUploads.containsKey(messageId)) {
      return UploadStatus.uploading;
    }
    return null;
  }

  /// Cancel upload for a specific message
  Future<void> cancelUpload(String messageId) async {
    try {
      // Remove from queue
      _uploadQueue.removeWhere((task) => task.messageId == messageId);

      // Remove from active uploads
      _activeUploads.remove(messageId);

      // Close progress controller
      _progressControllers[messageId]?.close();
      _progressControllers.remove(messageId);

      debugPrint('Upload cancelled for message: $messageId');
    } catch (e) {
      debugPrint('Error cancelling upload: $e');
    }
  }

  /// Get all active upload tasks
  List<UploadTask> getActiveUploads() {
    return _activeUploads.values.toList();
  }

  /// Get queued upload tasks
  List<UploadTask> getQueuedUploads() {
    return _uploadQueue.toList();
  }

  /// Clear all uploads (for logout or reset)
  Future<void> clearAllUploads() async {
    try {
      _uploadQueue.clear();
      _activeUploads.clear();

      // Close all progress controllers
      for (final controller in _progressControllers.values) {
        controller.close();
      }
      _progressControllers.clear();

      debugPrint('All uploads cleared');
    } catch (e) {
      debugPrint('Error clearing uploads: $e');
    }
  }

  /// Retry failed upload
  Future<void> retryUpload(String messageId) async {
    try {
      // Find the task in failed state and re-queue it
      // This would typically be called from UI when user taps retry
      debugPrint('Retry upload requested for message: $messageId');
      // Implementation depends on how you store failed tasks
    } catch (e) {
      debugPrint('Error retrying upload: $e');
    }
  }
}

/// Upload task model
class UploadTask {
  final String messageId;
  final String chatId;
  final String localImagePath;
  final String? thumbnailPath;
  final String? replyToMessageId;
  final String? replyToText;
  final String? replyToSenderName;
  final Map<String, dynamic>? metadata;
  final Function(UploadProgress)? onProgress;
  final Function(String uploadUrl)? onSuccess;
  final Function(String error)? onError;

  int retryCount = 0;
  DateTime createdAt = DateTime.now();

  UploadTask({
    required this.messageId,
    required this.chatId,
    required this.localImagePath,
    this.thumbnailPath,
    this.replyToMessageId,
    this.replyToText,
    this.replyToSenderName,
    this.metadata,
    this.onProgress,
    this.onSuccess,
    this.onError,
  });
}

/// Upload progress model
class UploadProgress {
  final String messageId;
  final UploadStatus status;
  final double progress; // 0.0 to 1.0
  final String? uploadUrl;
  final String? error;

  UploadProgress({
    required this.messageId,
    required this.status,
    required this.progress,
    this.uploadUrl,
    this.error,
  });
}

/// Upload status enum
enum UploadStatus { pending, uploading, completed, failed, cancelled }

/// Extension to get user-friendly status text
extension UploadStatusExtension on UploadStatus {
  String get displayText {
    switch (this) {
      case UploadStatus.pending:
        return 'Pending';
      case UploadStatus.uploading:
        return 'Uploading';
      case UploadStatus.completed:
        return 'Sent';
      case UploadStatus.failed:
        return 'Failed';
      case UploadStatus.cancelled:
        return 'Cancelled';
    }
  }

  IconData get icon {
    switch (this) {
      case UploadStatus.pending:
        return Icons.schedule;
      case UploadStatus.uploading:
        return Icons.cloud_upload;
      case UploadStatus.completed:
        return Icons.check;
      case UploadStatus.failed:
        return Icons.error;
      case UploadStatus.cancelled:
        return Icons.cancel;
    }
  }
}
