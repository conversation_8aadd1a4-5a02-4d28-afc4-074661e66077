import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:mr_garments_mobile/services/parallel_image_upload_service.dart';
import 'package:mr_garments_mobile/services/optimized_image_service.dart';

/// Performance testing service for image uploads
/// Use this to validate that 100 images can be sent in ~1 minute
class ImageUploadPerformanceTest {
  /// Test parallel upload performance with mock data
  static Future<PerformanceTestResult> testParallelUploadPerformance({
    required String chatId,
    int imageCount = 100,
    bool enableLogging = true,
  }) async {
    if (enableLogging) {
      debugPrint(
        '🚀 Starting parallel upload performance test with $imageCount images',
      );
    }

    final stopwatch = Stopwatch()..start();
    int completedUploads = 0;
    int failedUploads = 0;
    final List<String> uploadTimes = [];

    try {
      // Create mock processed images for testing
      final mockImages = _createMockProcessedImages(imageCount);

      if (enableLogging) {
        debugPrint('📸 Created $imageCount mock images for testing');
      }

      // Track individual upload times
      final individualStopwatch = Stopwatch();

      // Use the parallel upload service
      final result = await ParallelImageUploadService.uploadImagesInParallel(
        chatId: chatId,
        images: mockImages,
        onProgress: (completed, total, failed) {
          completedUploads = completed;
          failedUploads = failed;

          if (enableLogging && completed % 10 == 0) {
            final elapsed = stopwatch.elapsedMilliseconds / 1000;
            final rate = completed / elapsed;
            debugPrint(
              '📊 Progress: $completed/$total completed, $failed failed (${rate.toStringAsFixed(1)} uploads/sec)',
            );
          }
        },
        onImageStatusUpdate: (imageName, status) {
          if (status == UploadStatus.uploading) {
            individualStopwatch.reset();
            individualStopwatch.start();
          } else if (status == UploadStatus.completed) {
            individualStopwatch.stop();
            uploadTimes.add('${individualStopwatch.elapsedMilliseconds}ms');
          }
        },
      );

      stopwatch.stop();

      final totalTimeSeconds = stopwatch.elapsedMilliseconds / 1000;
      final uploadsPerSecond = completedUploads / totalTimeSeconds;

      if (enableLogging) {
        debugPrint('✅ Upload test completed!');
        debugPrint(
          '⏱️  Total time: ${totalTimeSeconds.toStringAsFixed(2)} seconds',
        );
        debugPrint(
          '📈 Upload rate: ${uploadsPerSecond.toStringAsFixed(1)} uploads/second',
        );
        debugPrint('✅ Successful: ${result.successfulUploads}');
        debugPrint('❌ Failed: ${result.failedUploads}');

        if (totalTimeSeconds <= 60) {
          debugPrint(
            '🎯 SUCCESS: Achieved target of 100 images in under 1 minute!',
          );
        } else {
          debugPrint('⚠️  WARNING: Took longer than 1 minute target');
        }
      }

      return PerformanceTestResult(
        success: true,
        totalImages: imageCount,
        successfulUploads: result.successfulUploads,
        failedUploads: result.failedUploads,
        totalTimeSeconds: totalTimeSeconds,
        uploadsPerSecond: uploadsPerSecond,
        metTarget: totalTimeSeconds <= 60,
        individualUploadTimes: uploadTimes,
      );
    } catch (e) {
      stopwatch.stop();

      if (enableLogging) {
        debugPrint('❌ Upload test failed: $e');
      }

      return PerformanceTestResult(
        success: false,
        totalImages: imageCount,
        successfulUploads: completedUploads,
        failedUploads: failedUploads,
        totalTimeSeconds: stopwatch.elapsedMilliseconds / 1000,
        uploadsPerSecond: 0,
        metTarget: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// Create mock processed images for testing
  static List<ProcessedImage> _createMockProcessedImages(int count) {
    final List<ProcessedImage> mockImages = [];

    for (int i = 0; i < count; i++) {
      // Create a small mock file for testing
      final mockFile = File.fromRawPath(Uint8List.fromList([1, 2, 3, 4, 5]));

      mockImages.add(
        ProcessedImage(
          originalPath: '/mock/path/image_$i.jpg',
          compressedFile: mockFile,
          fileSize: 1024, // 1KB mock size
          thumbnail: Uint8List.fromList([1, 2, 3]),
          name: 'mock_image_$i.jpg',
        ),
      );
    }

    return mockImages;
  }

  /// Compare performance between old sequential and new parallel methods
  static Future<ComparisonResult> compareUploadMethods({
    required String chatId,
    int imageCount = 20, // Smaller count for comparison
    bool enableLogging = true,
  }) async {
    if (enableLogging) {
      debugPrint('🔄 Starting upload method comparison test');
    }

    // Test parallel method
    final parallelResult = await testParallelUploadPerformance(
      chatId: chatId,
      imageCount: imageCount,
      enableLogging: false,
    );

    // Calculate estimated sequential time (based on typical 1 image per 12 seconds)
    final estimatedSequentialTime = imageCount * 12.0; // 12 seconds per image

    final speedupFactor =
        estimatedSequentialTime / parallelResult.totalTimeSeconds;

    if (enableLogging) {
      debugPrint('📊 Comparison Results:');
      debugPrint(
        '🐌 Estimated Sequential: ${estimatedSequentialTime.toStringAsFixed(1)}s',
      );
      debugPrint(
        '🚀 Parallel Actual: ${parallelResult.totalTimeSeconds.toStringAsFixed(1)}s',
      );
      debugPrint(
        '⚡ Speedup Factor: ${speedupFactor.toStringAsFixed(1)}x faster',
      );
    }

    return ComparisonResult(
      parallelResult: parallelResult,
      estimatedSequentialTimeSeconds: estimatedSequentialTime,
      speedupFactor: speedupFactor,
      parallelIsFaster:
          parallelResult.totalTimeSeconds < estimatedSequentialTime,
    );
  }
}

/// Result of performance test
class PerformanceTestResult {
  final bool success;
  final int totalImages;
  final int successfulUploads;
  final int failedUploads;
  final double totalTimeSeconds;
  final double uploadsPerSecond;
  final bool metTarget; // Whether it met the 1-minute target
  final List<String> individualUploadTimes;
  final String? errorMessage;

  PerformanceTestResult({
    required this.success,
    required this.totalImages,
    required this.successfulUploads,
    required this.failedUploads,
    required this.totalTimeSeconds,
    required this.uploadsPerSecond,
    required this.metTarget,
    this.individualUploadTimes = const [],
    this.errorMessage,
  });

  double get successRate =>
      totalImages > 0 ? successfulUploads / totalImages : 0.0;

  String get summary =>
      'Uploaded $successfulUploads/$totalImages images in ${totalTimeSeconds.toStringAsFixed(1)}s '
      '(${uploadsPerSecond.toStringAsFixed(1)} uploads/sec)';
}

/// Result of comparing upload methods
class ComparisonResult {
  final PerformanceTestResult parallelResult;
  final double estimatedSequentialTimeSeconds;
  final double speedupFactor;
  final bool parallelIsFaster;

  ComparisonResult({
    required this.parallelResult,
    required this.estimatedSequentialTimeSeconds,
    required this.speedupFactor,
    required this.parallelIsFaster,
  });

  String get summary =>
      'Parallel method is ${speedupFactor.toStringAsFixed(1)}x faster than sequential '
      '(${parallelResult.totalTimeSeconds.toStringAsFixed(1)}s vs ${estimatedSequentialTimeSeconds.toStringAsFixed(1)}s)';
}
